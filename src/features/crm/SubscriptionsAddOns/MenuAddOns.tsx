import { ADDONS_EQUIPMENT, ADDONS_STATUS, ADDONS_TV } from "@constants";
import { Divider, Tabs, Grid, SelectChangeEvent, Tab, Typography, Paper, Box } from "@mui/material";
import { IAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { ITigoCbsAccountsResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoCbsAccountsResponse";
import { IBillCycleState } from "../AccountDetails/AccountDetailsHeader/Billing/BillCycle/useBillCycle";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import AddOns from "./Components/AddOns";
import { useTranslation } from "react-i18next";
import { Fragment, useState } from "react";
import dayjs from "dayjs";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useAddonStore } from "./useAddonStore";
import { Loader } from "@common";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";

interface IPropsMenuAddons {
    filter: string;
    debouncedHandleChange: (event: SelectChangeEvent<string>) => void;
    names: { name: string; label: string }[];
    addOnsServices: IAddOns | undefined;
    accountsCbs: ITigoCbsAccountsResponse | undefined;
    actualBillCycle: {
        billCycleOpenDate: Date;
        billCycleEndDate: Date;
    };
    billCycleId?: string;
    calculateCycleDate: (cycleDay: number) => dayjs.Dayjs;
    billCycleState: IBillCycleState;
    classes: any;
    contactDetails: TContactDetails | undefined;
    getBillCycle: () => Promise<void>;
    handleActiveRetention: (addonCode: string, addonId: number) => void;
    removeAddOn: (addonsId: number, serviceId: number, cycleDate: string) => Promise<number | string>;
    onClickOpenOffer: () => void;
    validateCancelAddOns: (
        activateDate: string,
        _addOnStatus: string,
        billCycleEndDate: Date,
        billCycleOpenDate: Date
    ) => boolean;

    //---- PARAMETROS ADICIONALES QUE NECESITO PARA EL SCHEDULEINTSTALLATION
    scheduleSuccess: boolean;
    setCorrelationId: React.Dispatch<React.SetStateAction<string | undefined>>;
    setScheduleInstallation: React.Dispatch<React.SetStateAction<ISlot | undefined>>;
    setSuccessScheduleInstallation: React.Dispatch<React.SetStateAction<boolean>>;
}

const MenuAddOns = ({
    names,
    addOnsServices,
    accountsCbs,
    actualBillCycle,
    billCycleId,
    billCycleState,
    calculateCycleDate,
    classes,
    contactDetails,
    getBillCycle,
    handleActiveRetention,
    removeAddOn,
    validateCancelAddOns,
    onClickOpenOffer,
}: IPropsMenuAddons) => {
    const { t } = useTranslation(["customer"]);
    const [tabValue, setTabValue] = useState(names[0]?.name ?? "");
    const [showSchedule, setShowSchedule] = useState(false);
    

    const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };

    const refreshAddons = useAddonStore((state) => state.refreshAddons);

    return (
        <div>
            {refreshAddons ? (
                <Grid alignItems="center" container justifyContent="center" style={{ height: "30vh" }}>
                    <Loader />
                </Grid>
            ) : (
                <div>
                    <Grid alignItems="center" container direction="row" pb={2} spacing={2}>
                        <Grid item xs={12}>
                            <Tabs
                                aria-label="Add-ons tabs"
                                value={tabValue}
                                variant="fullWidth"
                                onChange={handleTabChange}
                            >
                                {names?.map(({ name, label }) => (
                                    <Tab
                                        disabled={label === "INTERNET"}
                                        key={name}
                                        label={label}
                                        sx={{ fontSize: "14px" }}
                                        value={name}
                                    />
                                ))}
                            </Tabs>
                        </Grid>
                    </Grid>
                    {addOnsServices?.collection.length !== 0 && addOnsServices !== undefined ? (
                        addOnsServices?.collection
                            .filter((val) => {
                                if (tabValue === ADDONS_TV) {
                                    return (
                                        val.itemGroupCode?.includes(ADDONS_TV) ||
                                        val.itemGroupCode?.includes(ADDONS_EQUIPMENT)
                                    );
                                }

                                return val.itemGroupCode?.includes(tabValue);
                            })
                            .sort((a, b) => {
                                return a.status === ADDONS_STATUS.ONGOING_TERMINATION
                                    ? 1
                                    : b.status === ADDONS_STATUS.ONGOING_TERMINATION
                                    ? -1
                                    : 0;
                            })
                            .map((element, index, array) => {
                                const isFirstOngoingTermination =
                                    element.status === ADDONS_STATUS.ONGOING_TERMINATION &&
                                    array.findIndex((e) => e.status === ADDONS_STATUS.ONGOING_TERMINATION) === index;

                                return (
                                    <Fragment key={element.id}>
                                        {isFirstOngoingTermination && (
                                            <Divider sx={{ pt: 2, pb: 2 }}>
                                                <Typography color="text.secondary" variant="subtitle2">
                                                    {t("customer:titleOngoingCancellationAddon")}
                                                </Typography>
                                            </Divider>
                                        )}
                                        <AddOns
                                            accountsCbs={accountsCbs}
                                            actualBillCycle={actualBillCycle}
                                            addOnsServices={addOnsServices}
                                            billCycleId={billCycleId ?? ""}
                                            billCycleState={billCycleState}
                                            calculateCycleDate={calculateCycleDate}
                                            classes={classes}
                                            contactDetails={contactDetails}
                                            data={element}
                                            getBillCycle={getBillCycle}
                                            handleActiveRetention={handleActiveRetention}
                                            index={index}
                                            removeAddOn={removeAddOn}
                                            validateCancelAddOns={validateCancelAddOns}
                                            onClickOpenOffer={onClickOpenOffer}
                                        />
                                    </Fragment>
                                );
                            })
                    ) : (
                        <div>
                            <Box
                                alignItems="center"
                                display="flex"
                                flexDirection="column"
                                justifyContent="center"
                                minHeight="250px"
                            >
                                <Paper
                                    elevation={0}
                                    sx={{
                                        maxWidth: "500px",
                                        width: "100%",
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <InfoOutlinedIcon color="primary" sx={{ fontSize: 45 }} />
                                    <Typography color="text.secondary" fontWeight="bold" mt={1} variant="h2">
                                        {t("customer:noAddOnsAvailable")}
                                    </Typography>
                                </Paper>
                            </Box>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default MenuAddOns;
